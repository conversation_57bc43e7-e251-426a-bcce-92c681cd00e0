# Button System Documentation

This document explains how to use the comprehensive button system in your webapp admin panel. **No more hardcoded colors!** 🎉

## 🎨 Design System Colors

Your button system uses these consistent colors:

- **Primary**: `#D7E1E4` (Light Blue) - For view, edit actions
- **Secondary**: `#D1E4EB` (Light Blue) - For cancel, neutral actions  
- **Tertiary**: `#016E01` (Green) - For create, save, approve actions
- **Danger**: Red variants - For delete, deactivate, suspend actions

## 🔘 Available Button Components

### 1. Common Button Components (Recommended)

Import from `@/components/ui/common-buttons`:

```tsx
import { 
  PrimaryButton, 
  SecondaryButton, 
  TertiaryButton, 
  DangerButton 
} from "@/components/ui/common-buttons";

// Basic usage
<PrimaryButton onClick={handleView}>View</PrimaryButton>
<SecondaryButton onClick={handleCancel}>Cancel</SecondaryButton>
<TertiaryButton onClick={handleCreate}>Create New</TertiaryButton>
<DangerButton onClick={handleDelete}>Delete</DangerButton>

// With icons
<TertiaryButton icon={Plus} onClick={handleCreate}>
  Add Team Member
</TertiaryButton>

<PrimaryButton icon={Eye} iconPosition="left" onClick={handleView}>
  View Details
</PrimaryButton>

// Different sizes
<TertiaryButton size="sm">Small</TertiaryButton>
<TertiaryButton size="default">Default</TertiaryButton>
<TertiaryButton size="lg">Large</TertiaryButton>
<TertiaryButton size="xl">Extra Large</TertiaryButton>

// Loading and disabled states
<TertiaryButton loading={true}>Saving...</TertiaryButton>
<PrimaryButton disabled={true}>Disabled</PrimaryButton>
```

### 2. Outline Variants

```tsx
import { 
  OutlinePrimaryButton, 
  OutlineSecondaryButton, 
  OutlineTertiaryButton, 
  OutlineDangerButton 
} from "@/components/ui/common-buttons";

<OutlinePrimaryButton>Outline Primary</OutlinePrimaryButton>
<OutlineTertiaryButton icon={Plus}>Outline Create</OutlineTertiaryButton>
```

### 3. Ghost Variants

```tsx
import { 
  GhostPrimaryButton, 
  GhostSecondaryButton, 
  GhostTertiaryButton, 
  GhostDangerButton 
} from "@/components/ui/common-buttons";

<GhostPrimaryButton>Ghost Primary</GhostPrimaryButton>
<GhostDangerButton icon={Trash2}>Ghost Delete</GhostDangerButton>
```

### 4. Raw Button Component

For maximum flexibility:

```tsx
import { Button } from "@/components/ui/button";

<Button variant="primary" size="lg">
  <Eye className="h-4 w-4" />
  Custom Button
</Button>

<Button variant="tertiary" size="sm">
  <Plus className="h-4 w-4" />
  Small Create
</Button>
```

## 🛠️ Utility Functions

### Automatic Button Variant Selection

```tsx
import { getButtonVariantForAction } from "@/lib/button-utils";

// Automatically get the right variant for actions
const variant = getButtonVariantForAction("create"); // Returns "tertiary"
const variant = getButtonVariantForAction("delete"); // Returns "danger"
const variant = getButtonVariantForAction("view");   // Returns "primary"

<Button variant={variant}>Action Button</Button>
```

### Pre-configured Button Configs

```tsx
import { BUTTON_CONFIGS } from "@/lib/button-utils";

// Use pre-configured button settings
<Button variant={BUTTON_CONFIGS.CREATE.variant}>
  {BUTTON_CONFIGS.CREATE.text}
</Button>
```

## 📋 Common Usage Patterns

### 1. Replace Your Old Hardcoded Buttons

❌ **Before (Hardcoded):**
```tsx
<button className="bg-[#016E01] text-white hover:bg-[#016E01]/90">
  Add Team Member
</button>
```

✅ **After (Design System):**
```tsx
<TertiaryButton icon={Plus} onClick={handleAdd}>
  Add Team Member
</TertiaryButton>
```

### 2. Action Button Arrays

❌ **Before:**
```tsx
const buttons = [
  {
    label: "View",
    className: "bg-[#D7E1E4] hover:bg-[#D7E1E4]/90 text-black",
  },
  {
    label: "Delete", 
    className: "bg-[#CD0035] hover:bg-[#CD0035]/90 text-white",
  }
];
```

✅ **After:**
```tsx
const buttons = [
  {
    label: "View",
    className: "bg-btn-primary hover:bg-btn-primary-hover text-btn-primary-text",
  },
  {
    label: "Delete",
    className: "bg-red-500 hover:bg-red-600 text-white",
  }
];

// Or even better, use the components directly:
<PrimaryButton icon={Eye}>View</PrimaryButton>
<DangerButton icon={Trash2}>Delete</DangerButton>
```

### 3. Form Actions

```tsx
// Form buttons
<div className="flex gap-2">
  <TertiaryButton type="submit" icon={Save}>
    Save Changes
  </TertiaryButton>
  <SecondaryButton type="button" icon={X} onClick={onCancel}>
    Cancel
  </SecondaryButton>
</div>
```

### 4. CRUD Operations

```tsx
// CRUD action buttons
<div className="flex gap-2">
  <TertiaryButton icon={Plus} onClick={onCreate}>Create</TertiaryButton>
  <PrimaryButton icon={Eye} onClick={onView}>View</PrimaryButton>
  <PrimaryButton icon={Edit} onClick={onEdit}>Edit</PrimaryButton>
  <DangerButton icon={Trash2} onClick={onDelete}>Delete</DangerButton>
</div>
```

## 🎯 When to Use Each Button Type

| Button Type | Use For | Examples |
|-------------|---------|----------|
| **Primary** | Main actions, viewing, editing | View, Edit, Submit |
| **Secondary** | Secondary actions, canceling | Cancel, Close, Back |
| **Tertiary** | Creating, positive actions | Create, Add, Save, Approve, Activate |
| **Danger** | Destructive actions | Delete, Remove, Deactivate, Suspend |

## 🔧 Customization

### Adding Custom Styles

```tsx
<TertiaryButton className="shadow-lg rounded-xl">
  Custom Styled Button
</TertiaryButton>
```

### Creating New Button Variants

If you need new variants, add them to `src/components/ui/button.tsx`:

```tsx
// In buttonVariants
"my-custom": "bg-purple-500 text-white hover:bg-purple-600"
```

## 📱 Responsive Design

All buttons are responsive by default. Use different sizes for different screen sizes:

```tsx
<TertiaryButton 
  size="sm" 
  className="md:size-default lg:size-lg"
>
  Responsive Button
</TertiaryButton>
```

## ✅ Best Practices

1. **Always use design system buttons** instead of hardcoded colors
2. **Use semantic button types** (Primary for view/edit, Tertiary for create, Danger for delete)
3. **Include icons** for better UX when appropriate
4. **Use consistent sizing** throughout your app
5. **Test in both light and dark modes**
6. **Use loading states** for async operations

## 🚀 Migration Checklist

- [ ] Replace all hardcoded button colors with design system components
- [ ] Update button arrays to use design system classes
- [ ] Use TertiaryButton for create/add actions (green)
- [ ] Use PrimaryButton for view/edit actions (light blue)
- [ ] Use DangerButton for delete/destructive actions (red)
- [ ] Add appropriate icons to buttons
- [ ] Test all button states (hover, disabled, loading)

Your buttons now use a consistent, maintainable design system! 🎉
