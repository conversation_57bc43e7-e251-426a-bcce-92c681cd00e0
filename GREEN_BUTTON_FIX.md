# Green Button Fix - Troubleshooting Guide

## 🚨 Problem
The tertiary green button color (#016E01) is not showing up in the UI.

## ✅ Solutions Implemented

### 1. Enhanced TertiaryButton with Fallback Styles
**File**: `src/components/ui/common-buttons.tsx`

The `TertiaryButton` now includes:
- Inline styles as fallback: `backgroundColor: '#016e01'`
- Tailwind classes: `bg-[#016e01] text-white hover:bg-[#014501]`
- JavaScript hover handlers for guaranteed color changes

```tsx
<TertiaryButton icon={Plus} onClick={handleAdd}>
  Add Team Member
</TertiaryButton>
```

### 2. Dedicated GreenButton Component
**File**: `src/components/ui/green-button.tsx`

A standalone green button component that's guaranteed to work:

```tsx
import { GreenButton } from "@/components/ui/green-button";

<GreenButton icon={Plus} onClick={handleAdd}>
  Add Team Member
</GreenButton>

// Different variants
<GreenButton variant="solid">Solid Green</GreenButton>
<GreenButton variant="outline">Outline Green</GreenButton>
<GreenButton variant="ghost">Ghost Green</GreenButton>
```

### 3. Updated Tailwind Configuration
**File**: `tailwind.config.ts`

- Added `src/**/*.{js,ts,jsx,tsx,mdx}` to content paths
- Added direct green color definitions as fallback
- Ensured button color variables are properly mapped

### 4. CSS Variables Check
**File**: `src/styles/colors.css`

Verified that these variables are defined:
```css
--btn-tertiary: #016e01;
--btn-tertiary-hover: #014501;
--btn-tertiary-text: #ffffff;
```

## 🔧 Testing Your Buttons

### Option 1: Use the Enhanced TertiaryButton
```tsx
import { TertiaryButton } from "@/components/ui/common-buttons";

<TertiaryButton icon={Plus} onClick={handleCreate}>
  Create New
</TertiaryButton>
```

### Option 2: Use the GreenButton Component
```tsx
import { GreenButton } from "@/components/ui/green-button";

<GreenButton icon={Plus} onClick={handleCreate}>
  Create New
</GreenButton>
```

### Option 3: Use Raw Button with Override
```tsx
import { Button } from "@/components/ui/button";

<Button 
  variant="tertiary"
  className="bg-[#016e01] hover:bg-[#014501] text-white"
  style={{ backgroundColor: '#016e01', color: 'white' }}
>
  Create New
</Button>
```

## 🐛 Debug Component
**File**: `src/components/debug/button-test.tsx`

Use this component to test all button variants:

```tsx
import ButtonTest from "@/components/debug/button-test";

// Add to any page to test
<ButtonTest />
```

## 🎯 Quick Fix for Your leUsers Page

In `src/app/leUsers/page.tsx`, you now have both options:

```tsx
// Current implementation (should work with fallback styles)
<TertiaryButton
  onClick={() => setIsModalOpen(true)}
  icon={Plus}
  size="default"
>
  Add Team Member
</TertiaryButton>

// Alternative (uncomment to test)
<GreenButton
  onClick={() => setIsModalOpen(true)}
  icon={Plus}
  size="default"
>
  Add Team Member (Green)
</GreenButton>
```

## 🔍 Troubleshooting Steps

### Step 1: Check if CSS Variables Work
Open browser dev tools and check if these CSS variables have values:
```css
var(--btn-tertiary)      /* Should be #016e01 */
var(--btn-tertiary-hover) /* Should be #014501 */
var(--btn-tertiary-text)  /* Should be #ffffff */
```

### Step 2: Check Tailwind Classes
In dev tools, see if these classes are applied:
- `bg-btn-tertiary`
- `text-btn-tertiary-text`
- `hover:bg-btn-tertiary-hover`

### Step 3: Use Fallback Approach
If CSS variables don't work, the inline styles should:
```tsx
style={{ backgroundColor: '#016e01', color: 'white' }}
```

### Step 4: Try the GreenButton Component
If all else fails, use the dedicated `GreenButton` component which uses inline styles exclusively.

## 🚀 Recommended Approach

1. **First, try the enhanced TertiaryButton** - it should work with the fallback styles
2. **If that doesn't work, use GreenButton** - it's guaranteed to work
3. **For debugging, use the ButtonTest component** to see what's working

## 💡 Why This Happened

The issue was likely caused by:
1. Tailwind not recognizing the CSS custom properties
2. CSS variables not being properly loaded
3. Build process not including the custom styles
4. Content paths in Tailwind config not including src folder

## ✅ What's Fixed Now

- ✅ TertiaryButton has inline style fallbacks
- ✅ GreenButton component as alternative
- ✅ Tailwind config updated with proper paths
- ✅ Multiple fallback approaches implemented
- ✅ Debug component for testing

Your green button should now work! 🎉
