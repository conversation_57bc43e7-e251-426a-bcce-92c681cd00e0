// Button utility functions for consistent button styling based on action types

export type ButtonVariant = 
  | "primary" 
  | "secondary" 
  | "tertiary" 
  | "danger" 
  | "outline" 
  | "outline-secondary" 
  | "outline-tertiary" 
  | "outline-danger"
  | "ghost"
  | "ghost-secondary"
  | "ghost-tertiary"
  | "ghost-danger";

export type ActionType = 
  | "view" 
  | "edit" 
  | "delete" 
  | "create" 
  | "save" 
  | "cancel" 
  | "submit" 
  | "approve" 
  | "reject" 
  | "activate" 
  | "deactivate" 
  | "suspend" 
  | "restore"
  | "download"
  | "upload"
  | "export"
  | "import";

/**
 * Get the appropriate button variant based on action type
 */
export function getButtonVariantForAction(action: ActionType): ButtonVariant {
  const actionMap: Record<ActionType, ButtonVariant> = {
    // Primary actions (light blue)
    view: "primary",
    edit: "primary",
    save: "primary",
    submit: "primary",
    download: "primary",
    export: "primary",
    
    // Secondary actions (light blue secondary)
    cancel: "secondary",
    upload: "secondary",
    import: "secondary",
    
    // Tertiary actions (green)
    create: "tertiary",
    approve: "tertiary",
    activate: "tertiary",
    restore: "tertiary",
    
    // Danger actions (red)
    delete: "danger",
    reject: "danger",
    deactivate: "danger",
    suspend: "danger",
  };
  
  return actionMap[action] || "primary";
}

/**
 * Get outline variant for action
 */
export function getOutlineVariantForAction(action: ActionType): ButtonVariant {
  const baseVariant = getButtonVariantForAction(action);
  
  switch (baseVariant) {
    case "primary":
      return "outline";
    case "secondary":
      return "outline-secondary";
    case "tertiary":
      return "outline-tertiary";
    case "danger":
      return "outline-danger";
    default:
      return "outline";
  }
}

/**
 * Get ghost variant for action
 */
export function getGhostVariantForAction(action: ActionType): ButtonVariant {
  const baseVariant = getButtonVariantForAction(action);
  
  switch (baseVariant) {
    case "primary":
      return "ghost";
    case "secondary":
      return "ghost-secondary";
    case "tertiary":
      return "ghost-tertiary";
    case "danger":
      return "ghost-danger";
    default:
      return "ghost";
  }
}

/**
 * Get button class names for action (for backward compatibility)
 */
export function getButtonClassForAction(action: ActionType): string {
  const variant = getButtonVariantForAction(action);
  
  switch (variant) {
    case "primary":
      return "bg-btn-primary text-btn-primary-text hover:bg-btn-primary-hover";
    case "secondary":
      return "bg-btn-secondary text-btn-secondary-text hover:bg-btn-secondary-hover";
    case "tertiary":
      return "bg-btn-tertiary text-btn-tertiary-text hover:bg-btn-tertiary-hover";
    case "danger":
      return "bg-red-500 text-white hover:bg-red-600";
    default:
      return "bg-btn-primary text-btn-primary-text hover:bg-btn-primary-hover";
  }
}

/**
 * Common button configurations for specific use cases
 */
export const BUTTON_CONFIGS = {
  // CRUD operations
  CREATE: {
    variant: "tertiary" as ButtonVariant,
    text: "Create",
  },
  EDIT: {
    variant: "primary" as ButtonVariant,
    text: "Edit",
  },
  VIEW: {
    variant: "primary" as ButtonVariant,
    text: "View",
  },
  DELETE: {
    variant: "danger" as ButtonVariant,
    text: "Delete",
  },
  
  // Form actions
  SAVE: {
    variant: "tertiary" as ButtonVariant,
    text: "Save",
  },
  CANCEL: {
    variant: "secondary" as ButtonVariant,
    text: "Cancel",
  },
  SUBMIT: {
    variant: "primary" as ButtonVariant,
    text: "Submit",
  },
  
  // Status actions
  ACTIVATE: {
    variant: "tertiary" as ButtonVariant,
    text: "Activate",
  },
  DEACTIVATE: {
    variant: "danger" as ButtonVariant,
    text: "Deactivate",
  },
  SUSPEND: {
    variant: "danger" as ButtonVariant,
    text: "Suspend",
  },
  
  // Approval actions
  APPROVE: {
    variant: "tertiary" as ButtonVariant,
    text: "Approve",
  },
  REJECT: {
    variant: "danger" as ButtonVariant,
    text: "Reject",
  },
} as const;
