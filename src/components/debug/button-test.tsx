"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { TertiaryButton } from "@/components/ui/common-buttons";
import { Plus } from "lucide-react";

/**
 * Button Test Component - Use this to debug button colors
 */
export function ButtonTest() {
  return (
    <div className="p-8 space-y-6 bg-white dark:bg-gray-900 rounded-lg">
      <h2 className="text-xl font-bold">Button Color Test</h2>
      
      {/* Test CSS Variables */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">CSS Variable Test</h3>
        <div className="space-y-2">
          <div 
            className="p-4 rounded"
            style={{ 
              backgroundColor: 'var(--btn-tertiary)', 
              color: 'var(--btn-tertiary-text)' 
            }}
          >
            Direct CSS Variable: var(--btn-tertiary)
          </div>
          <div 
            className="p-4 rounded"
            style={{ 
              backgroundColor: '#016e01', 
              color: 'white' 
            }}
          >
            Direct Hex: #016e01
          </div>
        </div>
      </div>

      {/* Test Tailwind Classes */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Tailwind Class Test</h3>
        <div className="space-y-2">
          <button className="px-4 py-2 rounded bg-btn-tertiary text-btn-tertiary-text hover:bg-btn-tertiary-hover">
            Tailwind: bg-btn-tertiary
          </button>
          <button className="px-4 py-2 rounded bg-green-600 text-white hover:bg-green-700">
            Tailwind: bg-green-600
          </button>
          <button 
            className="px-4 py-2 rounded text-white hover:opacity-90"
            style={{ backgroundColor: '#016e01' }}
          >
            Inline Style: #016e01
          </button>
        </div>
      </div>

      {/* Test Button Components */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Button Component Test</h3>
        <div className="space-y-2">
          <Button variant="tertiary">
            Button Component - Tertiary
          </Button>
          <TertiaryButton icon={Plus}>
            TertiaryButton Component
          </TertiaryButton>
          <Button 
            variant="tertiary" 
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            Button with Override Classes
          </Button>
        </div>
      </div>

      {/* Alternative Green Button */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Working Green Button (Alternative)</h3>
        <button 
          className="inline-flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium text-white transition-all duration-200 shadow-sm hover:shadow-md"
          style={{ 
            backgroundColor: '#016e01',
            '&:hover': { backgroundColor: '#014501' }
          }}
          onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#014501'}
          onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#016e01'}
        >
          <Plus className="h-4 w-4" />
          Working Green Button
        </button>
      </div>
    </div>
  );
}

export default ButtonTest;
