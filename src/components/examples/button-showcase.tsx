"use client";

import React from "react";
import {
  PrimaryButton,
  SecondaryButton,
  TertiaryButton,
  DangerButton,
  OutlinePrimaryButton,
  OutlineSecondaryButton,
  OutlineTertiaryButton,
  OutlineDangerButton,
  GhostPrimaryButton,
  GhostSecondaryButton,
  GhostTertiaryButton,
  GhostDangerButton,
} from "@/components/ui/common-buttons";
import { Button } from "@/components/ui/button";
import { Eye, Edit, Trash2, Plus, Save, X } from "lucide-react";

/**
 * Button Showcase Component
 * This component demonstrates all available button variants in your design system
 * Use this as a reference for implementing buttons throughout your application
 */
export function ButtonShowcase() {
  return (
    <div className="p-8 space-y-8 bg-bg-card rounded-lg border border-subtle">
      <h2 className="text-2xl font-bold text-text-primary mb-6">
        Design System Button Showcase
      </h2>

      {/* Solid Buttons */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-text-primary">
          Solid Buttons
        </h3>
        <div className="flex flex-wrap gap-4">
          <PrimaryButton icon={Eye}>Primary Button</PrimaryButton>
          <SecondaryButton icon={Edit}>Secondary Button</SecondaryButton>
          <TertiaryButton icon={Plus}>Tertiary Button</TertiaryButton>
          <DangerButton icon={Trash2}>Danger Button</DangerButton>
        </div>
      </div>

      {/* Outline Buttons */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-text-primary">
          Outline Buttons
        </h3>
        <div className="flex flex-wrap gap-4">
          <OutlinePrimaryButton icon={Eye}>
            Outline Primary
          </OutlinePrimaryButton>
          <OutlineSecondaryButton icon={Edit}>
            Outline Secondary
          </OutlineSecondaryButton>
          <OutlineTertiaryButton icon={Plus}>
            Outline Tertiary
          </OutlineTertiaryButton>
          <OutlineDangerButton icon={Trash2}>
            Outline Danger
          </OutlineDangerButton>
        </div>
      </div>

      {/* Ghost Buttons */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-text-primary">
          Ghost Buttons
        </h3>
        <div className="flex flex-wrap gap-4">
          <GhostPrimaryButton icon={Eye}>Ghost Primary</GhostPrimaryButton>
          <GhostSecondaryButton icon={Edit}>
            Ghost Secondary
          </GhostSecondaryButton>
          <GhostTertiaryButton icon={Plus}>Ghost Tertiary</GhostTertiaryButton>
          <GhostDangerButton icon={Trash2}>Ghost Danger</GhostDangerButton>
        </div>
      </div>

      {/* Button Sizes */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-text-primary">
          Button Sizes
        </h3>
        <div className="flex flex-wrap items-center gap-4">
          <TertiaryButton size="sm" icon={Plus}>
            Small
          </TertiaryButton>
          <TertiaryButton size="default" icon={Plus}>
            Default
          </TertiaryButton>
          <TertiaryButton size="lg" icon={Plus}>
            Large
          </TertiaryButton>
          <TertiaryButton size="xl" icon={Plus}>
            Extra Large
          </TertiaryButton>
        </div>
      </div>

      {/* Icon Positions */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-text-primary">
          Icon Positions
        </h3>
        <div className="flex flex-wrap gap-4">
          <PrimaryButton icon={Save} iconPosition="left">
            Save Changes
          </PrimaryButton>
          <PrimaryButton icon={Save} iconPosition="right">
            Save Changes
          </PrimaryButton>
          <SecondaryButton icon={X} iconPosition="left">
            Cancel
          </SecondaryButton>
        </div>
      </div>

      {/* Loading States */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-text-primary">
          Loading States
        </h3>
        <div className="flex flex-wrap gap-4">
          <TertiaryButton loading={true}>Loading Button</TertiaryButton>
          <PrimaryButton disabled={true}>Disabled Button</PrimaryButton>
        </div>
      </div>

      {/* Using Raw Button Component */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-text-primary">
          Raw Button Component
        </h3>
        <div className="flex flex-wrap gap-4">
          <Button variant="primary" size="default">
            <Eye className="h-4 w-4" />
            Raw Primary
          </Button>
          <Button variant="tertiary" size="lg">
            <Plus className="h-4 w-4" />
            Raw Tertiary Large
          </Button>
          <Button variant="outline-danger" size="sm">
            <Trash2 className="h-4 w-4" />
            Raw Outline Danger
          </Button>
        </div>
      </div>

      {/* Common Use Cases */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-text-primary">
          Common Use Cases
        </h3>
        <div className="space-y-4">
          {/* Form Actions */}
          <div className="flex gap-2">
            <TertiaryButton icon={Save}>Save</TertiaryButton>
            <SecondaryButton icon={X}>Cancel</SecondaryButton>
          </div>

          {/* CRUD Actions */}
          <div className="flex gap-2">
            <TertiaryButton icon={Plus}>Create New</TertiaryButton>
            <PrimaryButton icon={Eye}>View</PrimaryButton>
            <PrimaryButton icon={Edit}>Edit</PrimaryButton>
            <DangerButton icon={Trash2}>Delete</DangerButton>
          </div>

          {/* Status Actions */}
          <div className="flex gap-2">
            <TertiaryButton>Activate</TertiaryButton>
            <DangerButton>Deactivate</DangerButton>
            <DangerButton>Suspend</DangerButton>
          </div>
        </div>
      </div>

      {/* Color Reference */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-text-primary">
          Color Reference
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <p>
              <strong>Primary:</strong> #D7E1E4 (Light Blue)
            </p>
            <p>
              <strong>Secondary:</strong> #D1E4EB (Light Blue)
            </p>
            <p>
              <strong>Tertiary:</strong> #016E01 (Green)
            </p>
            <p>
              <strong>Danger:</strong> Red variants
            </p>
          </div>
          <div className="space-y-2">
            <p>
              <strong>Use Primary for:</strong> View, Edit actions
            </p>
            <p>
              <strong>Use Secondary for:</strong> Cancel, neutral actions
            </p>
            <p>
              <strong>Use Tertiary for:</strong> Create, Save, Approve actions
            </p>
            <p>
              <strong>Use Danger for:</strong> Delete, Deactivate, Suspend
              actions
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ButtonShowcase;
