"use client";

import React from "react";
import { Button } from "./button";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

// Common button configurations
export interface CommonButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  icon?: LucideIcon;
  iconPosition?: "left" | "right";
  size?: "sm" | "default" | "lg" | "xl";
  className?: string;
}

// Primary Button (Light Blue - #D7E1E4)
export function PrimaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="primary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

// Secondary Button (Light Blue - #D1E4EB)
export function SecondaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="secondary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

// Tertiary Button (Green - #016E01)
export function TertiaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="tertiary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

// Danger Button (Red for destructive actions)
export function DangerButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="danger"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

// Outline variants
export function OutlinePrimaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="outline"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

export function OutlineSecondaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="outline-secondary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

export function OutlineTertiaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="outline-tertiary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

export function OutlineDangerButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="outline-danger"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

// Ghost variants
export function GhostPrimaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="ghost"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

export function GhostSecondaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="ghost-secondary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

export function GhostTertiaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="ghost-tertiary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

export function GhostDangerButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="ghost-danger"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}
