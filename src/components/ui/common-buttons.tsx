"use client";

import React from "react";
import { But<PERSON> } from "./button";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

// Common button configurations
export interface CommonButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  icon?: LucideIcon;
  iconPosition?: "left" | "right";
  size?: "sm" | "default" | "lg" | "xl";
  className?: string;
}

// Primary Button (Light Blue - #D7E1E4)
export function PrimaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="primary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(
        // Fallback styles to ensure primary color works
        "bg-[#D7E1E4] text-[#1a1a1a] hover:bg-[#c1cdd2] focus:bg-[#c1cdd2]",
        className
      )}
      style={{
        backgroundColor: "#D7E1E4",
        color: "#1a1a1a",
      }}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.backgroundColor = "#c1cdd2";
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.backgroundColor = "#D7E1E4";
        }
      }}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

// Secondary Button (Light Blue - #D1E4EB)
export function SecondaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="secondary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(
        // Fallback styles to ensure secondary color works
        "bg-[#D1E4EB] text-[#1a1a1a] hover:bg-[#b8d4dd] focus:bg-[#b8d4dd]",
        className
      )}
      style={{
        backgroundColor: "#D1E4EB",
        color: "#1a1a1a",
      }}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.backgroundColor = "#b8d4dd";
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.backgroundColor = "#D1E4EB";
        }
      }}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

// Tertiary Button (Green - #016E01)
export function TertiaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="tertiary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(
        // Fallback styles to ensure green color works
        "bg-[#016e01] text-white hover:bg-[#014501] focus:bg-[#014501]",
        className
      )}
      style={{
        backgroundColor: "#016e01",
        color: "white",
      }}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.backgroundColor = "#014501";
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.backgroundColor = "#016e01";
        }
      }}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

// Danger Button (Red for destructive actions)
export function DangerButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="danger"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

// Outline variants
export function OutlinePrimaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="outline"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

export function OutlineSecondaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="outline-secondary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

export function OutlineTertiaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="outline-tertiary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(
        // Fallback styles for outline tertiary
        "border-[#016e01] text-[#016e01] bg-transparent hover:bg-[#016e01] hover:text-white",
        className
      )}
      style={{
        borderColor: "#016e01",
        color: "#016e01",
      }}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.backgroundColor = "#016e01";
          e.currentTarget.style.color = "white";
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.backgroundColor = "transparent";
          e.currentTarget.style.color = "#016e01";
        }
      }}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

export function OutlineDangerButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="outline-danger"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

// Ghost variants
export function GhostPrimaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="ghost"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

export function GhostSecondaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="ghost-secondary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

export function GhostTertiaryButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="ghost-tertiary"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(
        // Fallback styles for ghost tertiary
        "text-[#016e01] bg-transparent hover:bg-[#016e01]/10",
        className
      )}
      style={{
        color: "#016e01",
      }}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.backgroundColor = "rgba(1, 110, 1, 0.1)";
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.backgroundColor = "transparent";
        }
      }}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}

export function GhostDangerButton({
  children,
  onClick,
  disabled,
  loading,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  className,
}: CommonButtonProps) {
  return (
    <Button
      variant="ghost-danger"
      size={size}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(className)}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </Button>
  );
}
