"use client";

import React from "react";
import { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

export interface GreenButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  icon?: LucideIcon;
  iconPosition?: "left" | "right";
  size?: "sm" | "default" | "lg" | "xl";
  variant?: "solid" | "outline" | "ghost";
  className?: string;
  type?: "button" | "submit" | "reset";
}

/**
 * GreenButton - A guaranteed working green button component
 * Uses your design system green color (#016E01) with inline styles as fallback
 */
export function GreenButton({
  children,
  onClick,
  disabled = false,
  loading = false,
  icon: Icon,
  iconPosition = "left",
  size = "default",
  variant = "solid",
  className,
  type = "button",
}: GreenButtonProps) {
  const baseStyles = "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 focus:outline-none focus:ring-2 focus:ring-offset-2";
  
  const sizeStyles = {
    sm: "h-8 px-3 text-sm",
    default: "h-9 px-4 py-2 text-sm",
    lg: "h-10 px-6 text-base",
    xl: "h-12 px-8 text-base",
  };

  const getVariantStyles = () => {
    switch (variant) {
      case "outline":
        return {
          className: "bg-transparent border-2 text-[#016e01] hover:bg-[#016e01] hover:text-white focus:ring-[#016e01]/20",
          style: {
            borderColor: '#016e01',
            color: '#016e01',
          },
          hoverStyle: {
            backgroundColor: '#016e01',
            color: 'white',
          },
        };
      case "ghost":
        return {
          className: "bg-transparent text-[#016e01] hover:bg-[#016e01]/10 focus:ring-[#016e01]/20",
          style: {
            color: '#016e01',
          },
          hoverStyle: {
            backgroundColor: 'rgba(1, 110, 1, 0.1)',
          },
        };
      default: // solid
        return {
          className: "text-white shadow-sm hover:shadow-md focus:ring-[#016e01]/20",
          style: {
            backgroundColor: '#016e01',
            color: 'white',
          },
          hoverStyle: {
            backgroundColor: '#014501',
          },
        };
    }
  };

  const variantConfig = getVariantStyles();

  const handleMouseEnter = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!disabled && !loading && variantConfig.hoverStyle) {
      Object.assign(e.currentTarget.style, variantConfig.hoverStyle);
    }
  };

  const handleMouseLeave = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (!disabled && !loading) {
      Object.assign(e.currentTarget.style, variantConfig.style);
    }
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(
        baseStyles,
        sizeStyles[size],
        variantConfig.className,
        className
      )}
      style={variantConfig.style}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {Icon && iconPosition === "left" && <Icon className="h-4 w-4" />}
      {loading ? "Loading..." : children}
      {Icon && iconPosition === "right" && <Icon className="h-4 w-4" />}
    </button>
  );
}

// Convenience exports for different variants
export function GreenSolidButton(props: Omit<GreenButtonProps, 'variant'>) {
  return <GreenButton {...props} variant="solid" />;
}

export function GreenOutlineButton(props: Omit<GreenButtonProps, 'variant'>) {
  return <GreenButton {...props} variant="outline" />;
}

export function GreenGhostButton(props: Omit<GreenButtonProps, 'variant'>) {
  return <GreenButton {...props} variant="ghost" />;
}

export default GreenButton;
