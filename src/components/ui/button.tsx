import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default:
          "bg-btn-primary text-btn-primary-text shadow-xs hover:bg-btn-primary-hover transition-all duration-200",
        primary:
          "bg-btn-primary text-btn-primary-text shadow-xs hover:bg-btn-primary-hover transition-all duration-200",
        secondary:
          "bg-btn-secondary text-btn-secondary-text shadow-xs hover:bg-btn-secondary-hover transition-all duration-200",
        tertiary:
          "bg-btn-tertiary text-btn-tertiary-text shadow-xs hover:bg-btn-tertiary-hover transition-all duration-200",
        danger:
          "bg-red-500 text-white shadow-xs hover:bg-red-600 transition-all duration-200",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:aria-invalid:ring-destructive/40 dark:bg-destructive/60",
        outline:
          "border border-btn-primary text-btn-primary bg-transparent shadow-xs hover:bg-btn-primary hover:text-btn-primary-text transition-all duration-200",
        "outline-secondary":
          "border border-btn-secondary text-btn-secondary bg-transparent shadow-xs hover:bg-btn-secondary hover:text-btn-secondary-text transition-all duration-200",
        "outline-tertiary":
          "border border-btn-tertiary text-btn-tertiary bg-transparent shadow-xs hover:bg-btn-tertiary hover:text-btn-tertiary-text transition-all duration-200",
        "outline-danger":
          "border border-red-500 text-red-500 bg-transparent shadow-xs hover:bg-red-500 hover:text-white transition-all duration-200",
        ghost:
          "text-btn-primary hover:bg-btn-primary/10 hover:text-btn-primary transition-all duration-200",
        "ghost-secondary":
          "text-btn-secondary hover:bg-btn-secondary/10 hover:text-btn-secondary transition-all duration-200",
        "ghost-tertiary":
          "text-btn-tertiary hover:bg-btn-tertiary/10 hover:text-btn-tertiary transition-all duration-200",
        "ghost-danger":
          "text-red-500 hover:bg-red-500/10 hover:text-red-600 transition-all duration-200",
        link: "text-btn-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2 has-[>svg]:px-3",
        sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
        lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
        xl: "h-12 rounded-lg px-8 has-[>svg]:px-6 text-base",
        icon: "size-9",
        "icon-sm": "size-8",
        "icon-lg": "size-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : "button";

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };
