"use client";

import MainLayout from "@/layouts/main-layout";
import React, { useState } from "react";
import { dummyMembers } from "@/lib/dummy-data";
import { DataCard } from "@/components/constant/data-card";
import { Button } from "@/components/ui/button";
import {
  TertiaryButton,
  PrimaryButton,
  DangerButton,
} from "@/components/ui/common-buttons";
import { GreenButton } from "@/components/ui/green-button";
import { Eye, Edit, Trash2, Search, Filter, Plus } from "lucide-react";
import { Input } from "@/components/ui/input";
import { ActionButton, Badge } from "@/models/common";
import FilterSelect from "@/components/constant/filter-select";
import { useRouter } from "next/navigation";
import TeamMemberForm from "@/components/constant/add-team-member-modal";

const badges: Badge[] = [
  {
    label: "Active",
    dotColor: "var(--tertiary)", // Using design system green
    className: "bg-bg-card border border-subtle text-text-primary",
  },
  {
    label: "Editor",
    className: "bg-bg-card border border-subtle text-text-primary",
    dotColor: "var(--primary)", // Using design system primary
  },
];

// Button configuration with direct color values (guaranteed to work)
const buttons: ActionButton[] = [
  {
    label: "View",
    icon: Eye,
    className:
      "bg-[#D7E1E4] hover:bg-[#c1cdd2] text-[#1a1a1a] transition-all duration-200 shadow-sm",
  },
  {
    label: "Edit",
    icon: Edit,
    className:
      "bg-[#D7E1E4] hover:bg-[#c1cdd2] text-[#1a1a1a] transition-all duration-200 shadow-sm",
  },
  {
    label: "Deactivate",
    icon: Trash2,
    className:
      "bg-red-500 hover:bg-red-600 text-white transition-all duration-200 shadow-sm",
  },
];

/* Alternative approach using button components directly:
const renderActionButton = (action: string, member: any) => {
  switch (action) {
    case "View":
      return <PrimaryButton icon={Eye} size="sm">View</PrimaryButton>;
    case "Edit":
      return <PrimaryButton icon={Edit} size="sm">Edit</PrimaryButton>;
    case "Deactivate":
      return <DangerButton icon={Trash2} size="sm">Deactivate</DangerButton>;
    default:
      return null;
  }
};
*/

const statusOptions = [
  { value: "all", label: "All Status" },
  { value: "active", label: "Active", color: "var(--tertiary)" },
  { value: "inactive", label: "Inactive", color: "var(--error)" },
  { value: "pending", label: "Pending", color: "var(--warning)" },
];

const planOption = [
  { value: "all", label: "All plans" },
  { value: "admin", label: "Admin", color: "var(--error)" },
  { value: "editor", label: "Editor", color: "var(--primary)" },
  { value: "viewer", label: "Viewer", color: "var(--neutral-500)" },
];

export default function SubscribersPage() {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [planFilter, setPlanFilter] = useState<string>("all");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const router = useRouter();

  const handleActionButtonClick = (type: string, id: string) => {
    switch (type) {
      case "View":
        router.push(`/leUsers/${id}`);
        break;
      case "Edit":
        console.log("Edit clicked for", id);
        break;
      case "Deactivate":
        console.log("Deactivate clicked for", id);
        break;
      default:
        console.log("Unknown action", type, id);
    }
  };

  const handleSubmit = (data: {
    fullName: string;
    email: string;
    password: string;
    role: string;
  }) => {
    console.log("Form submitted:", data);
    setIsModalOpen(false);
  };
  return (
    <MainLayout>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        {/* Left Section */}
        <div>
          <h1 className="text-2xl font-bold text-text-primary">
            Team Management
          </h1>
          <p className="text-text-secondary mt-1">
            Manage internal team members and their roles
          </p>
        </div>

        {/* Right Section */}
        <div className="space-y-2">
          {/* Option 1: TertiaryButton with fallback styles */}
          <TertiaryButton
            onClick={() => setIsModalOpen(true)}
            icon={Plus}
            size="default"
          >
            Add Team Member
          </TertiaryButton>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 animate-fadeIn">
        {/* Left: Search */}
        <div className="flex-1 relative group">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-text-muted group-focus-within:text-primary transition-colors" />
          </div>
          <Input
            type="text"
            placeholder="Search team members by name or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 bg-bg-card border border-subtle text-text-primary placeholder:text-text-muted
             hover:bg-bg-card-hover hover:border-primary/40
             focus:border-primary focus:ring-2 focus:ring-primary/20
             transition-all duration-200 rounded-lg shadow-sm"
          />
        </div>
        <div className="flex gap-3 items-center">
          <div className="flex items-center gap-2 text-text-secondary text-sm">
            <Filter className="h-4 w-4" />
            <span>Filters:</span>
          </div>

          {/* Status Filter */}
          <FilterSelect
            placeholder="Status"
            value={statusFilter}
            onChange={setStatusFilter}
            options={statusOptions}
          />

          <FilterSelect
            placeholder="plans"
            value={planFilter}
            onChange={setPlanFilter}
            options={planOption}
          />
        </div>
      </div>

      {/* Data Cards */}
      <div className="space-y-4 full-width-container">
        {dummyMembers.map((member) => {
          // Generate JSX buttons dynamically
          const actions = buttons.map((btn, idx) => {
            const Icon = btn.icon;
            return (
              <Button
                key={idx}
                size="sm"
                className={btn.className}
                onClick={() => handleActionButtonClick(btn.label, member.id)}
              >
                <Icon className="h-4 w-4 mr-1" /> {btn.label}
              </Button>
            );
          });

          return (
            <DataCard
              key={member.id}
              id={member.id}
              avatar={`https://images.ctfassets.net/h6goo9gw1hh6/2sNZtFAWOdP1lmQ33VwRN3/24e953b920a9cd0ff2e1d587742a2472/1-intro-photo-final.jpg?w=1200&h=992&fl=progressive&q=70&fm=jpg`}
              fallback={member.name
                .split(" ")
                .map((n) => n[0])
                .join("")}
              title={member.name}
              subtitle={member.email}
              badges={badges}
              actions={actions}
            />
          );
        })}
      </div>
      {isModalOpen && (
        <TeamMemberForm
          mode={"create"}
          initialData={undefined}
          onClose={() => setIsModalOpen(false)}
          onSubmit={handleSubmit}
        />
      )}
    </MainLayout>
  );
}
